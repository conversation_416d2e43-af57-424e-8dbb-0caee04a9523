version: '3.3'

services:
  web:
    build: .
    container_name: marianska-chata
    expose:
      - "3000"
    volumes:
      - ./data:/app/data
    environment:
      - NODE_ENV=production
      - PORT=3000
    restart: unless-stopped
    networks:
      - marianska-net

  nginx:
    image: nginx:alpine
    container_name: marianska-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - /etc/letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - web
    restart: unless-stopped
    networks:
      - marianska-net

networks:
  marianska-net:
    driver: bridge