/*
 * Modern Booking System Design - Clean Version
 * Material Design 3 with Glassmorphism Effects
 * Responsive Grid Layout with Dark/Light Mode
 */

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Primary Colors - Material You */
  --primary-50: #f0f4ff;
  --primary-100: #e0eafe;
  --primary-200: #c9dcfd;
  --primary-300: #a6c5fa;
  --primary-400: #7ca7f5;
  --primary-500: #5085ed;
  --primary-600: #3c68e2;
  --primary-700: #3354cf;
  --primary-800: #3047a8;
  --primary-900: #2e3f84;

  /* Neutral Colors */
  --neutral-0: #ffffff;
  --neutral-10: #fdfcff;
  --neutral-20: #f4f1f4;
  --neutral-30: #e6e2e6;
  --neutral-40: #cac5ca;
  --neutral-50: #b0abb0;
  --neutral-60: #959096;
  --neutral-70: #7c777c;
  --neutral-80: #635f63;
  --neutral-90: #4a474a;
  --neutral-95: #343034;
  --neutral-99: #1c1b1f;

  /* Surface Colors */
  --surface-0: var(--neutral-10);
  --surface-1: #f7f4f7;
  --surface-2: #f2eff2;
  --surface-3: #edeaed;
  --surface-4: #eae7ea;
  --surface-5: #e6e3e6;

  /* Accent Colors */
  --accent-pink: #ff6b9d;
  --accent-blue: #4fc3f7;
  --accent-purple: #ba68c8;
  --accent-orange: #ff9800;
  --accent-green: #66bb6a;
  --accent-teal: #26a69a;

  /* Semantic Colors */
  --success: var(--accent-green);
  --warning: var(--accent-orange);
  --error: #f44336;
  --info: var(--accent-blue);

  /* Gray Scale (compatibility) */
  --gray-50: #F9FAFB;
  --gray-100: #F2F2F7;
  --gray-200: #E5E5EA;
  --gray-300: #D1D1D6;
  --gray-400: #C7C7CC;
  --gray-500: #8E8E93;
  --gray-600: #636366;
  --gray-700: #48484A;
  --gray-800: #3A3A3C;
  --gray-900: #1C1C1E;

  /* Success/Green Scale (compatibility) */
  --success-50: #ECFDF5;
  --success-100: #D1FAE5;
  --success-200: #A7F3D0;
  --success-300: #6EE7B7;
  --success-400: #34D399;
  --success-500: #10B981;
  --success-600: #059669;
  --success-700: #047857;
  --success-800: #065F46;
  --success-900: #064E3B;

  /* Elevation & Shadow */
  --elevation-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --elevation-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --elevation-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  --elevation-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  --elevation-5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);

  /* Border Radius */
  --radius-xs: 4px;
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-2xl: 32px;
  --radius-full: 9999px;

  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  --font-family-display: 'Poppins', var(--font-family-primary);

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* Animation */
  --transition-fast: 150ms ease-out;
  --transition-normal: 250ms ease-out;
  --transition-slow: 400ms ease-out;
  --spring: cubic-bezier(0.34, 1.56, 0.64, 1);

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --backdrop-blur: blur(20px);
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --surface-0: #121212;
  --surface-1: #1e1e1e;
  --surface-2: #232323;
  --surface-3: #252525;
  --surface-4: #272727;
  --surface-5: #2c2c2c;

  --neutral-10: #1c1b1f;
  --neutral-20: #343034;
  --neutral-90: #f4f1f4;
  --neutral-99: #ffffff;

  --glass-bg: rgba(18, 18, 18, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

/* ===== GOOGLE FONTS IMPORT ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  height: 100%;
}

body {
  font-family: var(--font-family-primary);
  background: linear-gradient(135deg,
    var(--surface-0) 0%,
    var(--surface-1) 50%,
    var(--surface-2) 100%);
  color: var(--neutral-90);
  line-height: 1.6;
  font-weight: 400;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* Animated Background */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, var(--primary-500) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, var(--accent-pink) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, var(--accent-blue) 0%, transparent 50%);
  opacity: 0.03;
  z-index: -1;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* ===== BUTTON SYSTEM ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: var(--text-sm);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  min-height: 44px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
}

/* Shine effect */
.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--transition-slow);
  z-index: 1;
}

.btn:hover::before {
  left: 100%;
}

/* Ripple effect */
.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all var(--transition-fast);
  transform: translate(-50%, -50%);
  z-index: 0;
}

.btn:active::after {
  width: 200%;
  height: 200%;
}

/* Button variants */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: var(--neutral-0);
  box-shadow: var(--elevation-2), 0 0 20px rgba(80, 133, 237, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--elevation-4), 0 0 30px rgba(80, 133, 237, 0.4);
}

.btn-primary:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: var(--elevation-2);
}

.btn-secondary {
  background: white;
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--primary-500);
  color: var(--primary-600);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-300);
}

.btn-secondary:active {
  transform: translateY(0) scale(0.99);
}

/* Additional button variants */
.btn-success {
  background: linear-gradient(135deg, var(--success), var(--accent-green));
  color: var(--neutral-0);
  box-shadow: var(--elevation-2), 0 0 20px rgba(102, 187, 106, 0.2);
}

.btn-success:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--elevation-4), 0 0 30px rgba(102, 187, 106, 0.4);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning), var(--accent-orange));
  color: var(--neutral-0);
  box-shadow: var(--elevation-2), 0 0 20px rgba(255, 152, 0, 0.2);
}

.btn-warning:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--elevation-4), 0 0 30px rgba(255, 152, 0, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
  font-weight: 600;
  border: 1px solid #bd2130;
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
  background: linear-gradient(135deg, #c82333, #bd2130);
}

/* Button sizes */
.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  min-height: 32px;
  font-weight: 600;
}

/* Admin panel cards */
.blocked-date-card {
  background: white;
  border: 1px solid var(--gray-200);
  color: var(--gray-900);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.blocked-date-card strong {
  color: var(--gray-900);
}

/* Code chips for Christmas access codes */
.code-chip {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  border: 2px solid var(--primary-300);
  color: var(--gray-900);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.code-chip span {
  color: var(--gray-900);
}

.code-chip button {
  background: var(--danger-500);
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
  font-weight: bold;
}

.code-chip button:hover {
  background: var(--danger-600);
  transform: scale(1.1);
}

.btn-large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-base);
  min-height: 56px;
  font-weight: 700;
}

/* Disabled state */
.btn:disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--elevation-1);
}

/* Icon buttons */
.btn-icon {
  width: 44px;
  height: 44px;
  padding: 0;
  border-radius: var(--radius-full);
}

.btn-icon.btn-small {
  width: 32px;
  height: 32px;
}

.btn-icon.btn-large {
  width: 56px;
  height: 56px;
}

/* ===== LAYOUT COMPONENTS ===== */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* ===== HEADER ===== */
.header {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--elevation-2);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: var(--transition-normal);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
}

.logo {
  font-family: var(--font-family-display);
  font-size: var(--text-2xl);
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-600), var(--accent-pink));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  transition: var(--transition-normal);
}

.logo:hover {
  transform: scale(1.05);
}

.nav {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

/* Language Switch */
.language-switch {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--surface-2);
  padding: var(--space-2);
  border-radius: var(--radius-full);
  box-shadow: var(--elevation-1);
}

.lang-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--neutral-70);
  min-width: 24px;
  text-align: center;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--neutral-40);
  transition: var(--transition-normal);
  border-radius: var(--radius-full);
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background: var(--neutral-0);
  transition: var(--transition-normal);
  border-radius: 50%;
  box-shadow: var(--elevation-1);
}

input:checked + .slider {
  background: var(--primary-500);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Theme Toggle */
.theme-toggle {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-full);
  background: var(--surface-3);
  color: var(--neutral-70);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
  box-shadow: var(--elevation-1);
}

.theme-toggle:hover {
  background: var(--surface-4);
  transform: scale(1.1);
}

/* ===== MAIN CONTENT ===== */
.main-content {
  flex: 1;
  max-width: 1600px;
  margin: 0 auto;
  padding: var(--space-8) var(--space-6);
  display: grid;
  grid-template-columns: 1fr 500px;
  gap: var(--space-6);
  align-items: flex-start;
  align-content: start;
}

@media (max-width: 1024px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
}

/* ===== CALENDAR SECTION ===== */
.calendar-section {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
  position: relative;
  margin-top: 0;
}

/* ===== BOOKING SECTION ===== */
.booking-section {
  width: 100%;
  min-width: 480px;
  margin-top: 0;
  padding-top: 0;
}

.booking-card {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--glass-shadow);
  width: 100%;
  margin-top: 0;
}

.booking-card h2 {
  margin-top: 0;
  padding-top: 0;
}

.calendar-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(80, 133, 237, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity var(--transition-slow);
  pointer-events: none;
  z-index: 0;
}

.calendar-section:hover {
  transform: translateY(-6px) scale(1.005);
  box-shadow: var(--elevation-5), 0 0 50px rgba(80, 133, 237, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
}

.calendar-section:hover::before {
  opacity: 1;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.month-title {
  font-family: var(--font-family-display);
  font-size: var(--text-3xl);
  font-weight: 600;
  color: var(--neutral-90);
  text-align: center;
  min-width: 200px;
}

.nav-btn {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: var(--radius-full);
  background: var(--surface-3);
  color: var(--neutral-70);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
  box-shadow: var(--elevation-1);
  position: relative;
  z-index: 100;
  pointer-events: all;
}

.nav-btn:hover {
  background: var(--primary-500);
  color: var(--neutral-0);
  transform: scale(1.1);
}

.nav-btn:active {
  transform: scale(0.95);
}

/* Calendar Grid */
.calendar-grid {
  display: grid;
  gap: var(--space-1);
  margin-top: var(--space-4);
}

.calendar-header-row {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--space-1);
  margin-bottom: var(--space-2);
}

.calendar-day-header {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--neutral-60);
  padding: 2px;
  margin-bottom: 2px;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--space-1);
}

.calendar-day {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  padding: 6px 4px;
  border-radius: var(--radius-md);
  cursor: default;
  transition: var(--transition-fast);
  position: relative;
  min-height: 90px;
  background: var(--surface-1);
  border: 2px solid transparent;
}

.calendar-day:hover {
  background: var(--surface-3);
  transform: scale(1.02);
}

.calendar-day.other-month {
  opacity: 0.3;
  pointer-events: none;
}

.calendar-day.today {
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  border-color: var(--primary-500);
}

.calendar-day.selected {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: var(--neutral-0);
  transform: scale(1.05);
  box-shadow: var(--elevation-3);
}

.calendar-day.christmas {
  background: linear-gradient(135deg, #ffcccc, #ffdddd);
  border-color: #ff8c00; /* Dark orange for better visibility */
  border-width: 3px; /* Make border thicker for better visibility */
}

.day-number {
  font-size: var(--text-sm);
  font-weight: 600;
  margin-bottom: var(--space-1);
}

/* Calendar day number styling */
.calendar-day-number {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--gray-800);
  line-height: 1;
}

/* Room indicators in calendar */
.calendar-day-rooms {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3px;
  padding: 4px;
  margin-top: 2px;
}

.room-indicator {
  font-size: 0.75rem;
  padding: 3px 1px;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
  transition: var(--transition-fast);
  font-weight: 700;
  min-width: 26px;
  height: 22px;
  line-height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-100);
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
}

.room-indicator.available {
  background: var(--success-100);
  color: var(--success-700);
  border-color: var(--success-300);
}

/* Small rooms - blue */
.room-indicator.room-small {
  background: #007bff !important;
  color: white !important;
  border: 2px solid #0056b3 !important;
  font-weight: 700;
}

/* Large rooms - green */
.room-indicator.room-large {
  background: #28a745 !important;
  color: white !important;
  border: 2px solid #1e7e34 !important;
  font-weight: 700;
}

.room-indicator.available:hover {
  transform: scale(1.1);
}

.room-indicator.available.room-small:hover {
  background: var(--info-200);
  border-color: var(--info-600);
}

.room-indicator.available.room-large:hover {
  background: var(--success-200);
  border-color: var(--success-600);
}

.room-indicator.booked {
  background: #ff8c00 !important;
  color: white !important;
  border-color: #ff6600 !important;
}

.room-indicator.blocked {
  background: repeating-linear-gradient(
    45deg,
    var(--danger-400),
    var(--danger-400) 3px,
    var(--danger-500) 3px,
    var(--danger-500) 6px
  ) !important;
  color: white !important;
  border: 2px solid var(--danger-600) !important;
  cursor: pointer;
  font-weight: 700;
  position: relative;
  overflow: hidden;
}

.room-indicator.blocked::after {
  content: '×';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  font-weight: bold;
  color: #ff0000;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.room-indicator.selected {
  background: #dc3545 !important;
  color: white !important;
  border: 2px solid #bd2130 !important;
  transform: scale(1.15);
  box-shadow: 0 3px 6px rgba(220, 53, 69, 0.4);
  font-weight: 700;
}

/* Override for selected rooms to keep red color */
.room-indicator.selected.room-small,
.room-indicator.selected.room-large {
  background: #dc3545 !important;
  border: 2px solid #bd2130 !important;
  color: white !important;
}

.room-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2px;
  width: 100%;
}

.room-cell {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-xs);
  transition: var(--transition-fast);
  cursor: pointer;
}

.room-cell.available {
  background: var(--success);
}

.room-cell.booked {
  background: #ff8c00 !important;
  color: white !important;
}

.room-cell.blocked {
  background: var(--neutral-50);
}

.room-cell.selected {
  background: var(--primary-500);
  transform: scale(1.2);
  box-shadow: 0 0 0 2px var(--neutral-0);
}

/* ===== BOOKING SECTION ===== */
.booking-section {
  position: sticky;
  top: calc(var(--space-20) + 60px);
}

.booking-card {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.booking-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-pink), var(--accent-blue));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.booking-card:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow: var(--elevation-5), 0 0 40px rgba(80, 133, 237, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.booking-card:hover::before {
  opacity: 1;
}

.booking-card h2 {
  font-family: var(--font-family-display);
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--neutral-90);
  margin-bottom: var(--space-6);
  text-align: center;
}

/* Selected Dates Display */
.selected-dates {
  background: var(--surface-2);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-6);
  border-left: 4px solid var(--primary-500);
}

.selected-dates:empty {
  display: none;
}

.selected-dates h3 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--primary-600);
}

/* Room Selection */
.room-selection {
  margin-bottom: var(--space-6);
}

/* Placeholder frame removed per user request */

.room-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-5);
  margin-bottom: var(--space-3);
  background: var(--surface-2);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--elevation-1);
}

.room-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(80, 133, 237, 0.1), transparent);
  transition: left var(--transition-normal);
}

.room-option:hover {
  background: var(--surface-3);
  border-color: var(--primary-300);
  transform: translateY(-2px) scale(1.01);
  box-shadow: var(--elevation-3);
}

.room-option:hover::before {
  left: 100%;
}

.room-option.selected {
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  border-color: var(--primary-500);
  transform: scale(1.02);
  box-shadow: var(--elevation-3), 0 0 20px rgba(80, 133, 237, 0.2);
}

.room-option.selected .room-name {
  color: var(--primary-700);
  font-weight: 700;
}

.room-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.room-name {
  font-weight: 600;
  color: var(--neutral-90);
}

.room-details {
  font-size: var(--text-sm);
  color: var(--neutral-60);
}

/* Price Calculator */
.price-calculator {
  background: var(--surface-2);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
}

.price-calculator h3 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--neutral-90);
}

.guest-type {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.guest-type label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  font-weight: 500;
}

.guest-counts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.guest-count-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.guest-count-item label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--neutral-70);
}

.number-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--neutral-30);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  background: var(--surface-1);
  color: var(--neutral-90);
  transition: all var(--transition-normal);
  min-height: 44px;
  box-sizing: border-box;
  position: relative;
}

.number-input:hover {
  border-color: var(--primary-300);
  background: var(--surface-2);
}

.number-input:focus {
  outline: none;
  border-color: var(--primary-500);
  background: var(--surface-0);
  box-shadow: 0 0 0 4px rgba(80, 133, 237, 0.15), var(--elevation-2);
  transform: scale(1.01);
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  background: var(--primary-100);
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-300);
}

.total-price span:first-child {
  font-weight: 600;
  color: var(--neutral-90);
}

.price-amount {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--primary-700);
}

/* ===== INPUT FIELDS ===== */
.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.input-group.full-width {
  grid-column: 1 / -1;
}

/* ===== FORM GRID ===== */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

/* Responsive form grid - single column on mobile */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
}

.input-group label {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--gray-800);
}

.input-group input,
.input-group select,
.input-group textarea {
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-600);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  background: white;
  color: var(--gray-900);
  transition: all var(--transition-normal);
  font-family: inherit;
  min-height: 44px;
  box-sizing: border-box;
  position: relative;
}

.input-group input:hover,
.input-group select:hover,
.input-group textarea:hover {
  border-color: var(--primary-300);
  background: var(--surface-2);
}

.input-group input:focus,
.input-group select:focus,
.input-group textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  background: var(--surface-0);
  box-shadow: 0 0 0 4px rgba(80, 133, 237, 0.15), var(--elevation-2);
  transform: scale(1.01);
}

.input-group input.invalid,
.input-group select.invalid,
.input-group textarea.invalid {
  border-color: var(--error);
  box-shadow: 0 0 0 4px rgba(244, 67, 54, 0.15);
}

.input-group input.valid,
.input-group select.valid,
.input-group textarea.valid {
  border-color: var(--success);
  box-shadow: 0 0 0 4px rgba(102, 187, 106, 0.15);
}

/* ===== MODALS ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-normal);
}

.modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  max-width: 90vw;
  max-height: 85vh;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), 0 0 100px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--gray-200);
  position: relative;
  transform: scale(0.8) translateY(40px);
  transition: all var(--transition-normal);
  color: var(--gray-900);
}

/* Ensure all text in modals is readable */
.modal-content h2,
.modal-content h3,
.modal-content h4 {
  color: var(--gray-900);
  font-weight: 600;
}

.modal-content p,
.modal-content div,
.modal-content span,
.modal-content label {
  color: var(--gray-800);
}

.modal-content strong {
  color: var(--gray-900);
  font-weight: 600;
}

.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-pink), var(--accent-blue), var(--primary-500));
  background-size: 200% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.modal.active .modal-content {
  transform: scale(1) translateY(0);
}

/* Room Info Modal specific styling */
.room-info-modal {
  overflow-y: auto !important;
  overflow-x: hidden;
  padding-bottom: 2rem;
}

.room-info-modal::-webkit-scrollbar {
  width: 8px;
}

.room-info-modal::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

.room-info-modal::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 4px;
}

.room-info-modal::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

.modal-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  width: 40px;
  height: 40px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-full);
  background: white;
  color: var(--gray-700);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  font-weight: 600;
  transition: var(--transition-normal);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modal-close:hover {
  background: var(--danger-500);
  color: white;
  transform: rotate(90deg) scale(1.1);
  border-color: var(--danger-500);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.modal h2 {
  font-family: var(--font-family-display);
  font-size: var(--text-2xl);
  font-weight: 600;
  margin-bottom: var(--space-6);
  color: var(--neutral-90);
}

.modal-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: flex-end;
  margin-top: var(--space-6);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .header-content {
    padding: var(--space-3) var(--space-4);
    flex-wrap: wrap;
  }

  .logo {
    font-size: var(--text-xl);
  }

  .main-content {
    padding: var(--space-4);
    gap: var(--space-4);
  }

  .calendar-section,
  .booking-card {
    padding: var(--space-4);
  }

  .calendar-day {
    min-height: 60px;
    padding: var(--space-1);
  }

  .room-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1px;
  }

  .room-cell {
    width: 12px;
    height: 12px;
  }
}

@media (max-width: 480px) {
  .calendar-header {
    flex-direction: column;
    gap: var(--space-4);
  }

  .nav-btn {
    width: 40px;
    height: 40px;
  }

  .month-title {
    font-size: var(--text-xl);
  }

  .guest-counts {
    grid-template-columns: 1fr;
  }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none; }
.invisible { visibility: hidden; }

.flex { display: flex; }
.grid { display: grid; }
.block { display: block; }
.inline-block { display: inline-block; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.m-0 { margin: 0; }
.p-0 { padding: 0; }

/* ===== ANIMATIONS ===== */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

@keyframes slideInUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInDown {
  0% {
    transform: translateY(-20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Add animation classes to components */
.calendar-day {
  animation: scaleIn 0.2s ease-out;
}

.booking-card {
  animation: slideInUp 0.4s ease-out;
}

.calendar-section {
  animation: fadeIn 0.5s ease-out;
}

.room-option {
  animation: slideInUp 0.3s ease-out;
}

.room-option:nth-child(2) {
  animation-delay: 0.1s;
}

.room-option:nth-child(3) {
  animation-delay: 0.2s;
}

.room-option:nth-child(4) {
  animation-delay: 0.3s;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.btn:focus-visible,
.calendar-day:focus-visible,
.room-option:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* ===== LEGEND TOGGLE BUTTON ===== */
.legend-toggle-container {
  margin-top: var(--space-4);
  display: flex;
  justify-content: center;
}

.legend-toggle-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  color: var(--neutral-80);
  font-size: var(--text-sm);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
  box-shadow: var(--elevation-1);
}

.legend-toggle-btn:hover {
  background: var(--surface-2);
  transform: translateY(-2px);
  box-shadow: var(--elevation-3);
  border-color: var(--primary-300);
}

.legend-toggle-btn:active {
  transform: translateY(0);
  box-shadow: var(--elevation-1);
}

.legend-toggle-icon {
  font-size: 1.2rem;
  color: var(--primary-500);
}

.legend-toggle-text {
  color: var(--neutral-80);
}

.legend-toggle-arrow {
  font-size: 0.8rem;
  color: var(--neutral-60);
  transition: transform var(--transition-fast);
}

.legend-toggle-btn[aria-expanded="true"] .legend-toggle-arrow {
  transform: rotate(180deg);
}

/* ===== COMPREHENSIVE CALENDAR LEGEND ===== */
.calendar-legend {
  margin-top: var(--space-4);
  padding: var(--space-5);
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--elevation-2);
  max-width: 100%;
  overflow: hidden;
  transition: all var(--transition-slow);
  transform-origin: top;
}

.calendar-legend.legend-hidden {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 0;
  opacity: 0;
  transform: scaleY(0);
  border-width: 0;
}

.calendar-legend.legend-visible {
  max-height: 2000px;
  opacity: 1;
  transform: scaleY(1);
}

.legend-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--neutral-90);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 2px solid var(--glass-border);
  text-align: center;
}

.legend-section {
  margin-bottom: var(--space-5);
}

.legend-section:last-child {
  margin-bottom: 0;
}

.legend-section-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--primary-600);
  margin-bottom: var(--space-3);
  padding-left: var(--space-2);
  border-left: 4px solid var(--primary-500);
}

.legend-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-3);
}

.legend-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--surface-1);
  border-radius: var(--radius-md);
  border: 1px solid var(--glass-border);
  transition: var(--transition-fast);
}

.legend-item:hover {
  background: var(--surface-2);
  transform: translateY(-2px);
  box-shadow: var(--elevation-2);
}

.legend-text {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.legend-label {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--neutral-80);
}

.legend-description {
  font-size: var(--text-xs);
  color: var(--neutral-60);
  line-height: 1.4;
}

.legend-indicator {
  width: 32px;
  height: 24px;
  border-radius: var(--radius-xs);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.75rem;
  position: relative;
  border: 2px solid;
  flex-shrink: 0;
}

.legend-available {
  background: var(--success-500);
  color: white;
  border-color: var(--success-600);
}

.legend-booked {
  background: #ff8c00;
  color: white;
  border-color: #ff6600;
}

.legend-blocked {
  background: repeating-linear-gradient(
    45deg,
    var(--danger-400),
    var(--danger-400) 3px,
    var(--danger-500) 3px,
    var(--danger-500) 6px
  );
  color: #ff0000;
  border: none;
  font-size: 1.2rem;
  font-weight: bold;
  position: relative;
}

.legend-selected {
  background: var(--success-500);
  color: white;
  border-color: var(--primary-500);
  border-width: 3px;
  box-shadow: 0 0 0 2px white, 0 0 0 4px var(--primary-500);
  transform: scale(1.1);
}

.legend-christmas {
  background: linear-gradient(135deg, #ffcccc, #ffdddd);
  border-color: #ff8c00;
  border-width: 3px;
  font-size: 1rem;
}

.legend-past {
  background: var(--gray-200);
  color: var(--gray-500);
  border-color: var(--gray-300);
  opacity: 0.7;
}

/* Usage Instructions */
.legend-instructions {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--surface-1);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--primary-500);
  font-size: var(--text-sm);
  color: var(--neutral-70);
}

.instruction-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .legend-toggle-btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }

  .legend-toggle-icon {
    font-size: 1rem;
  }

  .legend-items {
    grid-template-columns: 1fr;
  }

  .legend-item {
    padding: var(--space-2);
  }

  .legend-indicator {
    width: 28px;
    height: 20px;
    font-size: 0.7rem;
  }

  .legend-title {
    font-size: var(--text-lg);
    padding: var(--space-3);
  }

  .legend-section-title {
    font-size: var(--text-md);
  }

  .legend-section {
    margin-bottom: var(--space-3);
  }

  .calendar-legend {
    padding: var(--space-3);
  }

  .instruction-item {
    font-size: var(--text-xs);
    padding: var(--space-1) var(--space-2);
  }
}