<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rezervační systém - Chata Mariánská</title>
    <link rel="stylesheet" href="styles-modern.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">Chata Mariánská</h1>
                <nav class="nav">
                    <!-- Language Switch -->
                    <div class="language-switch">
                        <span class="lang-label">CZ</span>
                        <label class="switch">
                            <input type="checkbox" id="languageSwitch">
                            <span class="slider"></span>
                        </label>
                        <span class="lang-label">EN</span>
                    </div>

                    <button id="roomInfoBtn" class="btn btn-secondary" data-cs="Informace o pokojích" data-en="Room Information">
                        <span style="font-size: 1.2em; margin-right: 0.5rem;">ℹ️</span>
                        <span class="btn-text">Informace o pokojích</span>
                    </button>
                    <button id="bulkActionBtn" class="btn btn-primary" data-cs="Hromadná akce" data-en="Bulk Booking">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                            <line x1="16" y1="2" x2="16" y2="6"/>
                            <line x1="8" y1="2" x2="8" y2="6"/>
                            <line x1="3" y1="10" x2="21" y2="10"/>
                            <path d="M8 14h.01M12 14h.01M16 14h.01M8 18h.01M12 18h.01M16 18h.01"/>
                        </svg>
                        <span class="btn-text">Hromadná akce</span>
                    </button>
                    <button id="adminBtn" class="btn btn-secondary">Admin</button>
                </nav>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Calendar Section -->
            <section class="calendar-section">
                <div class="calendar-header">
                    <button id="prevMonth" class="nav-btn" aria-label="Předchozí měsíc">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M15 18l-6-6 6-6"/>
                        </svg>
                    </button>
                    <h2 id="currentMonth" class="month-title"></h2>
                    <button id="nextMonth" class="nav-btn" aria-label="Další měsíc">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M9 18l6-6-6-6"/>
                        </svg>
                    </button>
                </div>
                <div id="calendar" class="calendar-grid"></div>

                <!-- Legenda -->
                <div class="calendar-legend">
                    <h3 class="legend-title" data-translate="legend">Legenda</h3>
                    <div class="legend-items">
                        <div class="legend-item">
                            <div class="legend-indicator legend-available"></div>
                            <span data-translate="legendAvailable">Volný pokoj</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-indicator legend-booked"></div>
                            <span data-translate="legendBooked">Obsazený pokoj</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-indicator legend-blocked">×</div>
                            <span data-translate="legendBlocked">Blokovaný pokoj</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-indicator legend-christmas"></div>
                            <span data-translate="legendChristmas">Vánoční období</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Booking Form Section -->
            <section class="booking-section">
                <div class="booking-card">
                    <h2 data-translate="newBooking">Nová rezervace</h2>

                    <!-- Selected Dates Display -->
                    <div id="selectedDates" class="selected-dates"></div>

                    <!-- Room Selection -->
                    <div id="roomSelection" class="room-selection"></div>

                    <!-- Price Calculator -->
                    <div class="price-calculator">
                        <h3 data-translate="priceCalculation">Kalkulace ceny</h3>

                        <!-- Capacity display removed per user request -->

                        <!-- Room Guest Controls - Individual room cards -->
                        <div id="roomGuestControls" style="margin: 1rem 0;">
                            <!-- Room guest control cards will be added here dynamically -->
                        </div>

                        <!-- Total price moved below room cards -->
                        <div class="total-price" style="margin-top: 2rem; padding: 1.5rem; background: linear-gradient(135deg, var(--primary-50), var(--primary-100)); border-radius: var(--radius-lg); border: 2px solid var(--primary-200);">
                            <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                                <span style="font-size: 1.1rem; font-weight: 600; color: var(--gray-700);" data-translate="totalPrice">Celková cena:</span>
                                <span id="totalPrice" class="price-amount" style="font-size: 1.5rem; font-weight: 700; color: var(--primary-600); margin-left: auto; text-align: right;">0 Kč</span>
                            </div>
                        </div>
                    </div>

                    <!-- Create Booking Button -->
                    <div style="margin-top: 2rem; text-align: center;">
                        <button id="createBookingBtn" class="btn btn-primary" style="padding: 1rem 2rem; font-size: 1.1rem;">
                            <span data-translate="createBooking">Vytvořit rezervaci</span>
                        </button>
                    </div>
                </div>
            </section>
        </main>

        <!-- Booking Details Modal -->
        <div id="bookingModal" class="modal">
            <div class="modal-content">
                <button class="modal-close">&times;</button>
                <h2>Detail rezervace</h2>
                <div id="bookingDetails"></div>
                <div class="modal-actions">
                    <button id="contactOwner" class="btn btn-primary">Kontaktovat vlastníka</button>
                </div>
            </div>
        </div>

        <!-- Contact Form Modal -->
        <div id="contactModal" class="modal">
            <div class="modal-content">
                <button class="modal-close">&times;</button>
                <h2 data-translate="contactOwnerTitle">Kontaktovat vlastníka rezervace</h2>
                <div style="background: var(--gray-50); padding: 1rem; border-radius: var(--radius-md); margin-bottom: 1.5rem;">
                    <p style="font-size: 0.9rem; color: var(--gray-600); margin: 0;" data-translate="contactOwnerInfo">
                        Vaše zpráva bude odeslána vlastníkovi rezervace. Email vlastníka zůstává skrytý.
                    </p>
                </div>
                <form id="contactForm">
                    <div class="input-group">
                        <label for="contactEmail" data-translate="yourEmail">Váš email *</label>
                        <input type="email" id="contactEmail" required>
                    </div>
                    <div class="input-group">
                        <label for="contactMessage" data-translate="message">Zpráva *</label>
                        <textarea id="contactMessage" rows="5" required placeholder="Napište svou zprávu..."></textarea>
                    </div>
                    <button type="submit" class="btn-primary" data-translate="sendMessage">Odeslat zprávu</button>
                </form>
            </div>
        </div>

        <!-- Booking Form Modal -->
        <div id="bookingFormModal" class="modal">
            <div class="modal-content">
                <button class="modal-close">&times;</button>
                <h2 data-translate="completeBooking">Dokončení rezervace</h2>

                <!-- Booking Summary -->
                <div style="background: var(--gray-50); padding: 1rem; border-radius: var(--radius-md); margin-bottom: 2rem;">
                    <h3 style="font-size: 1rem; margin-bottom: 1rem;" data-translate="bookingSummary">Souhrn rezervace</h3>
                    <div id="bookingSummary" style="display: grid; gap: 0.5rem;">
                        <!-- Summary will be populated here -->
                    </div>
                </div>

                <!-- Booking Form -->
                <form id="bookingForm">
                    <h3 style="font-size: 1.1rem; margin-bottom: 1rem;" data-translate="billingDetails">Fakturační údaje</h3>

                    <div class="form-grid">
                        <div class="input-group">
                            <label for="name">Jméno a příjmení *</label>
                            <input type="text" id="name" name="name" required minlength="3"
                                   placeholder="např. Jan Novák"
                                   title="Zadejte celé jméno (min. 3 znaky)">
                        </div>

                        <div class="input-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" required
                                   placeholder="<EMAIL>"
                                   pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                                   title="Zadejte platnou emailovou adresu">
                        </div>

                        <div class="input-group">
                            <label for="phone">Telefon *</label>
                            <div style="display: flex; gap: 0.5rem;">
                                <select id="phonePrefix" name="phonePrefix" style="width: 80px; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: var(--radius-sm); font-size: 0.9rem;">
                                    <option value="+420">+420</option>
                                    <option value="+421">+421</option>
                                    <option value="+1">+1</option>
                                    <option value="+44">+44</option>
                                    <option value="+49">+49</option>
                                    <option value="+33">+33</option>
                                    <option value="+43">+43</option>
                                    <option value="+48">+48</option>
                                    <option value="+386">+386</option>
                                </select>
                                <input type="tel" id="phone" name="phone" required
                                       placeholder="*********"
                                       pattern="[0-9\s]{7,15}"
                                       title="Zadejte telefonní číslo (pouze číslice)"
                                       style="width: calc(100% - 85px);">
                            </div>
                        </div>

                        <div class="input-group">
                            <label for="company">Firma</label>
                            <input type="text" id="company" name="company"
                                   placeholder="Název firmy (nepovinné)">
                        </div>

                        <div class="input-group full-width">
                            <label for="address">Adresa *</label>
                            <input type="text" id="address" name="address" required minlength="5"
                                   placeholder="např. Hlavní 123"
                                   title="Zadejte ulici a číslo popisné">
                        </div>

                        <div class="input-group">
                            <label for="city">Město *</label>
                            <input type="text" id="city" name="city" required minlength="2"
                                   placeholder="např. Praha"
                                   title="Zadejte název města">
                        </div>

                        <div class="input-group">
                            <label for="zip">PSČ *</label>
                            <input type="text" id="zip" name="zip" required
                                   pattern="[0-9]{5}"
                                   placeholder="12345"
                                   maxlength="5"
                                   title="PSČ musí obsahovat přesně 5 číslic">
                        </div>

                        <div class="input-group">
                            <label for="ico">IČO</label>
                            <input type="text" id="ico" name="ico"
                                   pattern="[0-9]{8}"
                                   placeholder="12345678"
                                   maxlength="8"
                                   title="IČO musí obsahovat 8 číslic">
                        </div>

                        <div class="input-group">
                            <label for="dic">DIČ</label>
                            <input type="text" id="dic" name="dic"
                                   pattern="(CZ)?[0-9]{8,10}"
                                   placeholder="CZ12345678"
                                   title="DIČ ve formátu CZ12345678">
                        </div>
                    </div>

                    <div class="input-group full-width">
                        <label for="notes">Poznámky</label>
                        <textarea id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <div class="modal-actions" style="margin-top: 2rem;">
                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('bookingFormModal').classList.remove('active')" data-translate="cancel">
                            Zrušit
                        </button>
                        <button type="submit" class="btn btn-primary" data-translate="confirmBooking">
                            Potvrdit rezervaci
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Single Room Booking Modal -->
        <div id="singleRoomBookingModal" class="modal">
            <div class="modal-content" style="max-width: 600px; padding: 0; overflow: visible;">
                <button class="modal-close" onclick="closeSingleRoomModal()">&times;</button>

                <!-- Room Header -->
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 12px 12px 0 0;">
                    <h2 style="margin: 0; font-size: 1.8rem;" id="roomBookingTitle">Rezervace pokoje</h2>
                    <div id="roomBookingBadge" style="margin-top: 0.5rem;"></div>
                </div>

                <div style="padding: 2rem;">
                    <!-- Date Selection -->
                    <div style="margin-bottom: 2rem;">
                        <h3 style="margin-bottom: 1rem; color: #333;">Vyberte termín</h3>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #666;">Check-in</label>
                                <div id="checkInDisplay" style="padding: 0.75rem; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer; background: white;">
                                    <span style="color: #999;">Vyberte datum příjezdu</span>
                                </div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #666;">Check-out</label>
                                <div id="checkOutDisplay" style="padding: 0.75rem; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer; background: white;">
                                    <span style="color: #999;">Vyberte datum odjezdu</span>
                                </div>
                            </div>
                        </div>

                        <!-- Mini Calendar -->
                        <div id="miniCalendar" style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                            <!-- Calendar will be generated here -->
                        </div>

                        <!-- Nights Summary -->
                        <div id="nightsSummary" style="padding: 1rem; background: #e8f5e9; border-radius: 8px; display: none;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-weight: 600;">Počet nocí:</span>
                                <span id="nightsCount" style="font-size: 1.2rem; font-weight: bold; color: #2e7d32;">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Guest Type and Count -->
                    <div style="margin-bottom: 2rem;">
                        <h3 style="margin-bottom: 1rem; color: #333;">Typ hosta a počet osob</h3>

                        <!-- Guest Type -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin-bottom: 1rem;">
                            <label style="display: flex; align-items: center; padding: 0.75rem; background: white; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;">
                                <input type="radio" name="singleRoomGuestType" value="utia" checked style="margin-right: 0.5rem;">
                                <span>Zaměstnanec ÚTIA</span>
                            </label>
                            <label style="display: flex; align-items: center; padding: 0.75rem; background: white; border: 2px solid #e2e8f0; border-radius: 8px; cursor: pointer;">
                                <input type="radio" name="singleRoomGuestType" value="external" style="margin-right: 0.5rem;">
                                <span>Externí host</span>
                            </label>
                        </div>

                        <!-- Guest Count -->
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.25rem; font-size: 0.9rem; color: #666;">Dospělí</label>
                                <div style="display: flex; align-items: center; background: white; border: 1px solid #e2e8f0; border-radius: 6px;">
                                    <button onclick="adjustSingleRoomGuests('adults', -1)" style="padding: 0.5rem 0.75rem; border: none; background: transparent; cursor: pointer; font-size: 1.2rem;">-</button>
                                    <span id="singleRoomAdults" style="flex: 1; text-align: center; font-weight: 600;">1</span>
                                    <button onclick="adjustSingleRoomGuests('adults', 1)" style="padding: 0.5rem 0.75rem; border: none; background: transparent; cursor: pointer; font-size: 1.2rem;">+</button>
                                </div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.25rem; font-size: 0.9rem; color: #666;">Děti (3-12)</label>
                                <div style="display: flex; align-items: center; background: white; border: 1px solid #e2e8f0; border-radius: 6px;">
                                    <button onclick="adjustSingleRoomGuests('children', -1)" style="padding: 0.5rem 0.75rem; border: none; background: transparent; cursor: pointer; font-size: 1.2rem;">-</button>
                                    <span id="singleRoomChildren" style="flex: 1; text-align: center; font-weight: 600;">0</span>
                                    <button onclick="adjustSingleRoomGuests('children', 1)" style="padding: 0.5rem 0.75rem; border: none; background: transparent; cursor: pointer; font-size: 1.2rem;">+</button>
                                </div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.25rem; font-size: 0.9rem; color: #666;">Batolata (0-3)</label>
                                <div style="display: flex; align-items: center; background: white; border: 1px solid #e2e8f0; border-radius: 6px;">
                                    <button onclick="adjustSingleRoomGuests('toddlers', -1)" style="padding: 0.5rem 0.75rem; border: none; background: transparent; cursor: pointer; font-size: 1.2rem;">-</button>
                                    <span id="singleRoomToddlers" style="flex: 1; text-align: center; font-weight: 600;">0</span>
                                    <button onclick="adjustSingleRoomGuests('toddlers', 1)" style="padding: 0.5rem 0.75rem; border: none; background: transparent; cursor: pointer; font-size: 1.2rem;">+</button>
                                </div>
                            </div>
                        </div>

                        <!-- Capacity Warning -->
                        <div id="capacityWarning" style="margin-top: 0.5rem; padding: 0.5rem; background: #fff3cd; border-left: 4px solid #ffc107; border-radius: 4px; display: none;">
                            <span style="color: #856404; font-size: 0.9rem;">⚠️ Překročena kapacita pokoje</span>
                        </div>
                    </div>

                    <!-- Price Summary -->
                    <div style="border-top: 2px solid #e2e8f0; padding-top: 1.5rem; margin-bottom: 1.5rem;">
                        <h3 style="margin-bottom: 1rem; color: #333;">Souhrn ceny</h3>
                        <div id="priceBreakdown" style="background: #f8f9fa; padding: 1rem; border-radius: 8px;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                <span>Základní cena za pokoj:</span>
                                <span id="basePrice">0 Kč</span>
                            </div>
                            <div id="nightsPrice" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                <span>Počet nocí:</span>
                                <span>× <span id="nightsMultiplier">0</span></span>
                            </div>
                            <div id="adultsPrice" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; display: none;">
                                <span>Příplatek za dospělé:</span>
                                <span id="adultsSurcharge">0 Kč</span>
                            </div>
                            <div id="childrenPrice" style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; display: none;">
                                <span>Příplatek za děti:</span>
                                <span id="childrenSurcharge">0 Kč</span>
                            </div>
                            <div style="border-top: 1px solid #dee2e6; margin-top: 0.75rem; padding-top: 0.75rem; display: flex; justify-content: space-between; font-weight: bold; font-size: 1.1rem;">
                                <span>Celkem:</span>
                                <span id="totalPrice" style="color: #2e7d32;">0 Kč</span>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div style="display: flex; gap: 1rem;">
                        <button onclick="closeSingleRoomModal()" style="flex: 1; padding: 0.75rem; background: #e2e8f0; color: #333; border: none; border-radius: 8px; font-weight: 600; cursor: pointer;">
                            Zrušit
                        </button>
                        <button id="confirmSingleRoomBtn" onclick="confirmSingleRoomBooking()" style="flex: 2; padding: 0.75rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 8px; font-weight: 600; cursor: pointer;" disabled>
                            Pokračovat k údajům
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Room Info Modal -->
        <div id="roomInfoModal" class="modal">
            <div class="modal-content room-info-modal" style="max-width: 900px; max-height: 85vh; overflow-y: auto;">
                <button class="modal-close">&times;</button>
                <h2 data-translate="roomInfoTitle">Informace o pokojích</h2>

                <div style="margin-top: 2rem;">
                    <h3 style="color: var(--primary-color); margin-bottom: 1.5rem;" data-translate="roomCapacity">Kapacita pokojů</h3>
                    <div id="roomCapacityGrid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem; background: linear-gradient(135deg, var(--gray-50), var(--gray-100)); padding: 1.5rem; border-radius: var(--radius-md); border: 1px solid var(--gray-200);">
                        <!-- Room capacity will be dynamically loaded here -->
                    </div>
                    <div style="margin-top: 1rem; text-align: center; font-weight: 600; color: var(--gray-700);">
                        <span id="totalCapacityText" data-translate="totalCapacity">Celková kapacita: 26 lůžek</span>
                    </div>
                </div>

                <div style="margin-top: 2rem;">
                    <h3 style="color: var(--primary-color); margin-bottom: 1.5rem;" data-translate="priceList">Ceník</h3>
                    <div id="priceListContent">
                        <!-- Price list will be dynamically loaded here -->
                    </div>
                </div>

                <!-- Bulk Booking Price List -->
                <div style="margin-top: 2rem;">
                    <h3 style="color: #8b5cf6; margin-bottom: 1.5rem;" data-translate="bulkPriceList">Ceník hromadné rezervace</h3>
                    <div style="background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%); padding: 1.5rem; border-radius: var(--radius-md); border: 2px solid #d8b4fe;">
                        <div id="bulkPriceListContent">
                            <!-- Bulk price list will be dynamically loaded here -->
                        </div>
                        <div style="margin-top: 1rem; padding: 0.75rem; background: rgba(34, 197, 94, 0.1); border-radius: 6px; border-left: 4px solid #22c55e;">
                            <p style="font-size: 0.9rem; color: #15803d; margin: 0;" data-translate="bulkPriceNote">
                                <strong>Poznámka:</strong> Hromadná rezervace znamená pronájem celé chaty pro jeden termín. Děti do 3 let jsou vždy zdarma.
                            </p>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 2rem; padding: 1rem; background: var(--gray-50); border-radius: var(--radius-md);">
                    <h4 style="color: var(--warning-color); margin-bottom: 0.5rem;" data-translate="christmasTitle">🎄 Vánoční období</h4>
                    <p style="font-size: 0.95rem; color: var(--gray-700);" data-translate="christmasInfo">
                        Rezervace během vánočních prázdnin podléhají speciálním pravidlům.
                        Zaměstnanci ÚTIA mohou rezervovat max. 1-2 pokoje do 30.9. příslušného roku.
                    </p>
                </div>
            </div>
        </div>

        <!-- Bulk Booking Modal -->
        <div id="bulkBookingModal" class="modal">
            <div class="modal-content" style="max-width: 1000px; max-height: 90vh; overflow-y: auto;">
                <button class="modal-close">&times;</button>
                <h2 data-translate="bulkBookingTitle">Hromadná rezervace celé chaty</h2>

                <div id="bulkModalPricingInfo" style="display: none;">
                    <!-- Dynamic bulk pricing info will be loaded here -->
                </div>

                <!-- Bulk Booking Calendar Section -->
                <div style="margin-bottom: 2rem;">
                    <h3 style="color: var(--primary-color); margin-bottom: 1rem;" data-translate="bulkDateSelection">Výběr termínů pro hromadnou rezervaci</h3>
                    <div style="background: var(--gray-50); padding: 1rem; border-radius: var(--radius-md); margin-bottom: 1rem;">
                        <p style="font-size: 0.9rem; color: var(--gray-600); margin: 0;" data-translate="bulkDateSelectionInfo">
                            Vyberte dny v kalendáři. Šedé dny mají rezervace a nelze je vybrat. Zelené dny jsou plně volné pro hromadnou rezervaci.
                        </p>
                    </div>

                    <!-- Bulk Calendar Navigation -->
                    <div class="calendar-header" style="margin-bottom: 1rem;">
                        <button id="bulkPrevMonth" class="nav-btn" aria-label="Předchozí měsíc">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M15 18l-6-6 6-6"/>
                            </svg>
                        </button>
                        <h4 id="bulkCurrentMonth" class="month-title" style="margin: 0; font-size: 1.1rem;"></h4>
                        <button id="bulkNextMonth" class="nav-btn" aria-label="Další měsíc">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M9 18l6-6-6-6"/>
                            </svg>
                        </button>
                    </div>

                    <!-- Bulk Calendar Grid -->
                    <div id="bulkCalendar" class="calendar-grid" style="border: 1px solid var(--gray-300); border-radius: var(--radius-md); background: white;">
                        <!-- Bulk calendar will be rendered here -->
                    </div>
                </div>

                <!-- Guest Information -->
                <div id="bulkGuestInfo" style="margin-bottom: 2rem; display: none;">
                    <h3 style="color: var(--primary-color); margin-bottom: 1rem;" data-translate="guestInfo">Informace o hostech</h3>

                    <div class="form-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                        <div class="input-group">
                            <label data-translate="guestTypeLabel">Typ hostů</label>
                            <select id="bulkGuestType" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: var(--radius-sm); font-size: 0.9rem;">
                                <option value="utia" data-translate="employeeLabel">Zaměstnanci ÚTIA</option>
                                <option value="external" data-translate="externalLabel">Externí hosté</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <label data-translate="adults">Dospělí</label>
                            <input type="number" id="bulkAdults" min="1" value="1" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: var(--radius-sm);">
                        </div>
                        <div class="input-group">
                            <label data-translate="children">Děti (3-18 let)</label>
                            <input type="number" id="bulkChildren" min="0" value="0" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: var(--radius-sm);">
                        </div>
                    </div>

                    <!-- Price calculation -->
                    <div class="total-price" style="margin-top: 1.5rem; padding: 1.5rem; background: linear-gradient(135deg, var(--primary-50), var(--primary-100)); border-radius: var(--radius-lg); border: 2px solid var(--primary-200);">
                        <!-- First: Detailed breakdown -->
                        <div id="bulkPriceBreakdown" style="margin-bottom: 1rem; font-size: 0.9rem; color: var(--gray-600);">
                            <!-- Price breakdown will be shown here -->
                        </div>
                        <!-- Second: Total price -->
                        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%; padding-top: 1rem; border-top: 2px solid var(--primary-200);">
                            <span style="font-size: 1.1rem; font-weight: 600; color: var(--gray-700);" data-translate="bulkTotalPriceLabel">Celková cena za vybrané dny:</span>
                            <span id="bulkTotalPrice" class="price-amount" style="font-size: 1.5rem; font-weight: 700; color: var(--primary-600); margin-left: auto; text-align: right;">0 Kč</span>
                        </div>
                    </div>
                </div>

                <!-- Action buttons -->
                <div class="modal-actions" style="margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="document.getElementById('bulkBookingModal').classList.remove('active')" data-translate="cancel">
                        Zrušit
                    </button>
                    <button id="proceedToBulkBooking" class="btn btn-primary" style="display: none;" data-translate="proceedToBooking">
                        Pokračovat k rezervaci
                    </button>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        <div id="successMessage" class="success-message"></div>
    </div>

    <script src="data.js"></script>
    <script src="translations.js"></script>
    <script src="app.js"></script>
</body>
</html>